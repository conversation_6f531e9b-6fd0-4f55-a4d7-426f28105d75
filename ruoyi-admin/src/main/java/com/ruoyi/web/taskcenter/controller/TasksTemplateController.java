package com.ruoyi.web.taskcenter.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.web.taskcenter.domain.*;
import com.ruoyi.web.taskcenter.service.OpsTaskAttrBasicReplicaService;
import com.ruoyi.web.taskcenter.service.OpsTaskGenInfoService;
import com.ruoyi.web.taskcenter.service.OpsTaskTemplateService;
import com.ruoyi.web.taskcenter.util.ExcelHandler;
import com.ruoyi.web.taskcenter.util.MessageConstant;
import com.ruoyi.web.taskcenter.util.R;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 任务模板相关接口
 */
@RestController
@RequestMapping("/taskTemplate")
@RequiredArgsConstructor
public class TasksTemplateController {


    private final OpsTaskTemplateService taskTemplateService;

    private final ISysUserService systemUserService;

    private final OpsTaskAttrBasicReplicaService replicaService;

    private final ExcelHandler excelHandler;

    private final OpsTaskGenInfoService genInfoService;

    /**
     * 模板列表分页模糊查询
     *
     * @param templateName   模板名称
     * @param templateStatus 模板状态
     * @param templateType   模板类型
     * @param page           页码
     * @param pageSize       页面大小
     * @return IPage
     */
    @GetMapping("/listByPage")
    public R<Object> listByPage(@RequestParam(value = "templateName", required = false) String templateName,
                                @RequestParam(value = "templateStatus", required = false) String templateStatus,
                                @RequestParam(value = "templateType", required = false) String templateType,
                                @RequestParam(value = "page", required = false, defaultValue = "1") int page,
                                @RequestParam(value = "pageSize", required = false, defaultValue = "10") int pageSize
    ) {
        //分页模型
        Page<OpsTaskTemplate> pageModel = new Page<>(page, pageSize);
        //查询条件组装
        LambdaQueryWrapper<OpsTaskTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.hasText(templateName), OpsTaskTemplate::getTemplateName, templateName);
        queryWrapper.eq(StringUtils.hasText(templateStatus), OpsTaskTemplate::getTemplateStatus, templateStatus);
        queryWrapper.eq(StringUtils.hasText(templateType), OpsTaskTemplate::getTemplateType, templateType);
        queryWrapper.orderByDesc(OpsTaskTemplate::getCreateTime);
        //权限过滤
        ConditionTaskDTO dto = genInfoService.taskSpecialAuthFilter(null);
        if (dto == null || (dto.getPostIds() == null && dto.getType() != 4)) {
            return R.data(new Page<OpsTaskAttrBasic>(page, pageSize));
        }
        if (dto.getType() != 4) {
            queryWrapper.in(OpsTaskTemplate::getOrgId, dto.getPostIds());
        }
        //执行查询
        Map<String, String> mapper = systemUserService.findNameIdMapping();
        IPage<TaskTemplateVO> res = taskTemplateService.page(pageModel, queryWrapper)
                .convert(i -> new TaskTemplateVO().convertVO(i, mapper));
        return R.data(res);
    }


    /**
     * @return array
     */
    @GetMapping("/list")
    public R<Object> templateList(@RequestParam(value = "self", required = false) Boolean self) {
        ConditionTaskDTO dto = genInfoService.taskSpecialAuthFilter(null);
        LambdaQueryWrapper<OpsTaskTemplate> queryWrapper = new LambdaQueryWrapper<>();
        if (dto == null || (dto.getPostIds() == null && dto.getType() != 4)) {
            return R.data(new ArrayList<>());
        }
        if (Objects.isNull(self)) {
            return R.data(taskTemplateService.list(queryWrapper));
        }
        if (dto.getType() != 4) {
            queryWrapper.in(OpsTaskTemplate::getOrgId, dto.getPostIds());
        }
        return R.data(taskTemplateService.list(queryWrapper));
    }

    //作废接口
    @GetMapping("/editStatus")
    public R<Object> editStatus(@RequestParam("id") String id, @RequestParam("status") Integer status) {
        OpsTaskTemplate upModel = new OpsTaskTemplate();
        upModel.setId(id);
        upModel.setTemplateStatus(status);
        taskTemplateService.updateById(upModel);
        return R.success(MessageConstant.UPDATE_SUCCESS);
    }

    /**
     * 模板保存/编辑接口
     *
     * @return msg
     */
    @PostMapping("/save")
    public R<Object> save(@RequestBody TemplateVO vo) {
        taskTemplateService.saveOrUpdateAll(vo);
        return R.success(MessageConstant.ADD_SUCCESS);
    }

    //查看-所有下级任务表格图示,平铺展示
    @GetMapping("/templateView")
    public R<Object> view(@RequestParam("id") String id) {
        List<OpsTaskAttrBasicReplica> res = replicaService.viewList(id);
        return R.data(res);
    }

    //复制-模板本身与全部关联任务单元复制
    @GetMapping("/templateCopy")
    public R<Object> templateCopy(@RequestParam("id") String id) {
        //复制模板
        //复制关系与replica
        taskTemplateService.copy(id);
        return R.success(MessageConstant.COPY_SUCCESS);
    }


    @GetMapping("/detail")
    public R<Object> detail(@RequestParam("id") String id) {
        TemplateVO vo = new TemplateVO();
        OpsTaskTemplate template = taskTemplateService.getById(id);
        List<OpsTaskAttrBasicReplica> res = replicaService.viewList(id);
        vo.setTemplate(template);
        vo.setList(res);
        return R.data(vo);
    }

    //生成任务- 按照模板  已完成，接口迁移到任务中心下边


    //导入模板
    @PostMapping("/import")
    public R<Object> templateImport(@RequestParam("orgId") String orgId,
                                    @RequestParam("ownerId") String ownerId,
                                    @RequestParam("ownerType") String ownerType,
                                    @RequestParam("ownerVal") String ownerVal,
                                    @RequestParam("templateName") String templateName,
                                    @RequestParam("file") MultipartFile excelFile) throws IOException {

        List<OpsTaskAttrBasicReplica> cap = new ArrayList<>();
        excelHandler.processExcelFile(excelFile, cap);
        //默认手动
        taskTemplateService.templateImport(cap, templateName, "manual", orgId, ownerId, ownerType, ownerVal);
        return R.success(MessageConstant.ADD_SUCCESS);
    }

    @GetMapping("/delete")
    public R<Object> delete(@RequestParam("id") String id) {
        taskTemplateService.deleteTemplate(id);
        return R.success(MessageConstant.DELETE_SUCCESS);
    }
}
