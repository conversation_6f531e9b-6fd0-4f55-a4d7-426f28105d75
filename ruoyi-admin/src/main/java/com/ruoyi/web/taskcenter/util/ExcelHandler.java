package com.ruoyi.web.taskcenter.util;

import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.web.taskcenter.domain.OpsTaskAttrBasicReplica;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;


import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class ExcelHandler {


    private final ISysUserService userService;

    private final ISysDeptService opsSysOrgService;




    public void processExcelFile(MultipartFile file, List<OpsTaskAttrBasicReplica> cap) throws IOException {
        // 验证文件类型
        validateExcelFileType(file);
        // 读取并验证Excel内容
        try (InputStream inputStream = file.getInputStream()) {
            Workbook workbook = new XSSFWorkbook(inputStream);
            Sheet sheet = workbook.getSheetAt(0);
            // 校验表头
            validateHeader(sheet);
            // 校验时间格式
            validateTimeFormat(sheet);
            //取数
            readList(sheet, cap);
            //关闭流
            workbook.close();
        }
    }

    private void readList(Sheet sheet, List<OpsTaskAttrBasicReplica> cap) {
        Map<String,String> nameId=userService.findNameIdMapping();
        Map<String,Long> orgId=opsSysOrgService.selectDeptList(null).stream().collect(Collectors.toMap(SysDept::getDeptName,SysDept::getDeptId));
        // 遍历行
        for (int i = sheet.getFirstRowNum() + 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue; // 跳过空行
            // 遍历列
            OpsTaskAttrBasicReplica item = new OpsTaskAttrBasicReplica();
            item.setTaskNo(getCellValueAsString(row.getCell(0)).replace(".0",""));// 序号
            item.setTaskName(getCellValueAsString(row.getCell(1)));// 内容
            //经办
            String owner=getCellValueAsString(row.getCell(2));
            if(StringUtils.hasText(owner)){
                item.setTaskOwnerVal(owner);// 经办
                if(nameId.containsKey(owner)){
                    item.setTaskOwnerType("2");
                    item.setTaskOwnerId(nameId.get(owner));
                }
                if(orgId.containsKey(owner)){
                    item.setTaskOwnerType("1");
                    item.setTaskOwnerId(String.valueOf(orgId.get(owner)));
                }
            }
            String check=getCellValueAsString(row.getCell(3));
            if(StringUtils.hasText(check)) {
                item.setTaskCheckVal(check);// 复核
                if(nameId.containsKey(check)){
                    item.setTaskCheckType("2");
                    item.setTaskCheckId(nameId.get(check));
                    item.setTaskCheckReq("1");
                }
                if(orgId.containsKey(check)){
                    item.setTaskCheckType("1");
                    item.setTaskCheckId(String.valueOf(orgId.get(check)));
                    item.setTaskCheckReq("1");
                    item.setCheckOrgId(String.valueOf(orgId.get(check)));
                }
            }else {
                item.setTaskCheckReq("0");
            }

            item.setTaskStartTime(cn.hutool.core.date.DateUtil.parse(getCellValueAsString(row.getCell(4)), "HH:mm:ss")); // 开始时间
            item.setTaskEndTime(cn.hutool.core.date.DateUtil.parse(getCellValueAsString(row.getCell(5)), "HH:mm:ss")); // 截止时间
            item.setTaskDesc(getCellValueAsString(row.getCell(6)));// 备注说明
            item.setTaskPriority(priorityParse(getCellValueAsString(row.getCell(7))));//任务优先级  高中低 1 2 3
            //填充默认值
            //默认日常任务
            item.setTaskType("daily");
            //默认触发类型为工作日
            item.setTaskTriggerType("daily");
            //默认任务不能延期 0
            item.setTaskDeferredType("0");
            //默认任务不需要上传附件 0
            item.setTaskAttachmentsType("0");
            //默认任务等级 2 一般
            item.setTaskLevel("2");
            //默认任务不需要稽核
            item.setTaskAuditType("0");
            //默认任务完成方式 手动
            item.setTaskCompleteType("manual");
            //默认通知方式
            item.setTaskWarnNotice("web");
            //默认不统计
            item.setWorkAmountFlag(0);
            //默认批量完成允许
            item.setRequiredItem("{\"batchCompletion\":\"0\"}");
            //默认没有明细
            item.setImportStatus(0);
            //默认可见
            item.setAccessLevel(1);
            cap.add(item);
        }

    }

    private String priorityParse(String cellValueAsString) {
        if(!StringUtils.hasText(cellValueAsString)){
            return "3";
        }
        if(cellValueAsString.equals("高")){
            return "1";
        }
        if(cellValueAsString.equals("中")){
            return "2";
        }
        if(cellValueAsString.equals("低")){
            return "3";
        }
        return "2";
    }

    private void validateExcelFileType(MultipartFile file) {
        if (!Arrays.asList("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "application/vnd.ms-excel").contains(file.getContentType())) {
            throw new IllegalArgumentException("无效文件类型");
        }
    }

    private void validateHeader(Sheet sheet) {
        Row headerRow = sheet.getRow(0);
        List<String> requiredColumns = Arrays.asList("序号", "内容", "经办", "复核", "开始时间", "截止时间", "说明");

        for (String columnName : requiredColumns) {
            Cell cell = headerRow.getCell(requiredColumns.indexOf(columnName));
            if (cell == null || !cell.getStringCellValue().equals(columnName)) {
                throw new IllegalArgumentException("无效标题");
            }
        }
    }

    private void validateTimeFormat(Sheet sheet) {
        Row dataRow = sheet.getRow(1); // 假设数据从第二行开始
        String startTime = getCellValueAsString(dataRow.getCell(4));
        String endTime = getCellValueAsString(dataRow.getCell(5));
        if (!startTime.matches("\\d{2}:\\d{2}:\\d{2}") || !endTime.matches("\\d{2}:\\d{2}:\\d{2}")) {
            throw new IllegalArgumentException("无效时间格式，必须为: HH:mm:ss");
        }
    }

    private final static SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");

    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return ""; // 处理空单元格
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    Date date=cell.getDateCellValue();
                    return sdf.format(date);
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
}

