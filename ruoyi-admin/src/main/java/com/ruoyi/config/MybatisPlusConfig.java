package com.ruoyi.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.io.VFS;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.boot.autoconfigure.SpringBootVFS;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import com.ruoyi.framework.config.MyBatisConfig;

import javax.sql.DataSource;

@Configuration
@EnableTransactionManagement
@MapperScan(basePackages = {"com.ruoyi.**.mapper"})  // 扩展扫描范围，覆盖所有 mapper
public class MybatisPlusConfig {

    @Autowired
    private Environment env;

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();
        mybatisPlusInterceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MARIADB));
        return mybatisPlusInterceptor;
    }

    /**
     * 自定义SqlSessionFactory，确保类型别名正确扫描
     */
    @Bean
    @Primary
    public SqlSessionFactory sqlSessionFactory(DataSource dataSource) throws Exception {
        // 设置VFS实现类，解决jar包中类扫描问题
        VFS.addImplClass(SpringBootVFS.class);

        MybatisSqlSessionFactoryBean sessionFactory = new MybatisSqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);

        // 设置类型别名包，使用自定义的包扫描逻辑
        String typeAliasesPackage = env.getProperty("mybatis-plus.type-aliases-package");
        if (typeAliasesPackage != null) {
            // 使用MyBatisConfig的包扫描逻辑来处理通配符
            typeAliasesPackage = MyBatisConfig.setTypeAliasesPackage(typeAliasesPackage);
            sessionFactory.setTypeAliasesPackage(typeAliasesPackage);
        }

        // 设置mapper文件位置
        String mapperLocations = env.getProperty("mybatis-plus.mapper-locations");
        if (mapperLocations != null) {
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            sessionFactory.setMapperLocations(resolver.getResources(mapperLocations));
        }

        return sessionFactory.getObject();
    }
}